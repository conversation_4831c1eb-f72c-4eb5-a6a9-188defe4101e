// app/admin/dashboard/page.js
// This page is for administrators to manage users.
// Access to this page is protected by the middleware.

"use client"; // This component runs on the client side

import { useSession, signOut } from "next-auth/react";
import { useState, useEffect } from "react";
import Link from "next/link";

export default function AdminDashboardPage() {
  const { data: session, status } = useSession();
  const [users, setUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    // Fetch users only if the session is loaded and user is an admin
    if (status === "authenticated" && session?.user?.role === "admin") {
      const fetchUsers = async () => {
        setLoadingUsers(true);
        setError("");
        try {
          // This would be an API route to fetch users from your database
          // For simplicity, we'll simulate fetching from MongoDB directly via an API route
          const response = await fetch("/api/admin/users");
          const data = await response.json();

          if (response.ok) {
            setUsers(data.users);
          } else {
            setError(data.message || "Failed to fetch users.");
          }
        } catch (err) {
          console.error("Error fetching users:", err);
          setError("An error occurred while fetching users.");
        } finally {
          setLoadingUsers(false);
        }
      };
      fetchUsers();
    } else if (status === "authenticated" && session?.user?.role !== "admin") {
      // If authenticated but not admin, it means middleware failed or user navigated directly
      setError("You are not authorized to view this page.");
      setLoadingUsers(false);
    } else if (status === "unauthenticated") {
      // This case should be handled by middleware
      setError("You need to be signed in to view this page.");
      setLoadingUsers(false);
    }
  }, [status, session]); // Re-run when session status or content changes

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <p className="text-xl text-gray-700">Loading session...</p>
      </div>
    );
  }

  if (status === "unauthenticated" || (status === "authenticated" && session?.user?.role !== "admin")) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <h1 className="text-4xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-lg text-gray-700 mb-6">
            {error || "You do not have administrative privileges to access this page."}
          </p>
          <Link href="/dashboard" className="text-blue-500 hover:underline">
            Go to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center bg-gray-100 p-4">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-4xl mt-10">
        <h1 className="text-3xl font-bold text-gray-800 mb-6 text-center">Admin Dashboard</h1>

        {session && (
          <div className="text-center mb-6">
            <p className="text-lg text-gray-700">
              Logged in as: <span className="font-semibold">{session.user.name || session.user.email}</span> (Role: <span className="font-medium capitalize">{session.user.role}</span>)
            </p>
            <p className="text-gray-600">Your User ID: {session.user.id}</p>
          </div>
        )}

        <h2 className="text-2xl font-semibold text-gray-800 mb-4">Manage Users</h2>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <strong className="font-bold">Error! </strong>
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        {loadingUsers ? (
          <p className="text-gray-700 text-center">Loading users...</p>
        ) : users.length === 0 ? (
          <p className="text-gray-700 text-center">No users found.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border border-gray-200 rounded-lg">
              <thead>
                <tr className="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
                  <th className="py-3 px-6 text-left">ID</th>
                  <th className="py-3 px-6 text-left">Name</th>
                  <th className="py-3 px-6 text-left">Email</th>
                  <th className="py-3 px-6 text-left">Role</th>
                  <th className="py-3 px-6 text-center">Actions</th>
                </tr>
              </thead>
              <tbody className="text-gray-700 text-sm font-light">
                {users.map((user) => (
                  <tr key={user._id} className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="py-3 px-6 text-left whitespace-nowrap">{user._id}</td>
                    <td className="py-3 px-6 text-left">{user.name}</td>
                    <td className="py-3 px-6 text-left">{user.email}</td>
                    <td className="py-3 px-6 text-left capitalize">{user.role}</td>
                    <td className="py-3 px-6 text-center">
                      {/* Example actions - you can add edit/delete functionality here */}
                      <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded-lg text-xs mr-2">
                        Edit
                      </button>
                      <button className="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded-lg text-xs">
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        <div className="mt-8 flex justify-center space-x-4">
          <Link href="/dashboard" className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg focus:outline-none focus:shadow-outline transition duration-200 text-center">
            Back to User Dashboard
          </Link>
          <button
            onClick={() => signOut({ callbackUrl: "/" })}
            className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg focus:outline-none focus:shadow-outline transition duration-200"
          >
            Sign Out
          </button>
        </div>
      </div>
    </div>
  );
}
