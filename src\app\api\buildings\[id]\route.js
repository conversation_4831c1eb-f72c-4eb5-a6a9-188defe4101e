// src/app/api/buildings/[id]/route.js
// API routes for individual building operations

import { NextResponse } from "next/server";
import { auth } from "../../../../auth";
import dbConnect from "../../../../libs/mongoDb/connectToLuyariDB";
import { Building } from "../../../../libs/mongoDb/models/Building";
import mongoose from "mongoose";

/**
 * GET /api/buildings/[id] - Get single building by ID
 */
export async function GET(request, segmentData) {
  const params = await segmentData.params
  const id = params.id;
  
  try {
    const session = await auth();
    
    // if (!session || !session.user) {
    //   return NextResponse.json(
    //     { message: "Authentication required" },
    //     { status: 401 }
    //   );
    // }

    await dbConnect();

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { message: "Invalid building ID" },
        { status: 400 }
      );
    }

    const building = await Building.findById(id).lean();

    if (!building) {
      return NextResponse.json(
        { message: "Building not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ building });

  } catch (error) {
    console.error("Error fetching building:", error);
    return NextResponse.json(
      { message: "Internal server error", error: error.message },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/buildings/[id] - Update existing building
 */
export async function PUT(request, { params }) {
  try {
    const session = await auth();
    
    // if (!session || !session.user) {
    //   return NextResponse.json(
    //     { message: "Authentication required" },
    //     { status: 401 }
    //   );
    // }

    await dbConnect();

    const { id } = params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { message: "Invalid building ID" },
        { status: 400 }
      );
    }

    const updateData = await request.json();

    // Remove _id and timestamps from update data if present
    delete updateData._id;
    delete updateData.createdAt;
    delete updateData.updatedAt;

    // Validate buildingType if provided
    if (updateData.buildingType) {
      const validBuildingTypes = ['multi-storey', 'single-storey', 'multi-residence'];
      if (!validBuildingTypes.includes(updateData.buildingType)) {
        return NextResponse.json(
          { message: "Invalid building type" },
          { status: 400 }
        );
      }
    }

    // Validate buildingSummary structure if provided
    if (updateData.buildingSummary) {
      const requiredSummaryFields = ['length', 'width', 'baths', 'levels', 'cars', 'beds'];
      for (const field of requiredSummaryFields) {
        if (updateData.buildingSummary[field] === undefined || updateData.buildingSummary[field] === null) {
          return NextResponse.json(
            { message: `Missing required buildingSummary field: ${field}` },
            { status: 400 }
          );
        }
      }
    }

    // Check if projectTitle is being changed and if it conflicts
    if (updateData.projectTitle) {
      const existingBuilding = await Building.findOne({ 
        projectTitle: updateData.projectTitle,
        _id: { $ne: id }
      });
      
      if (existingBuilding) {
        return NextResponse.json(
          { message: "A building with this project title already exists" },
          { status: 409 }
        );
      }
    }

    const building = await Building.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    if (!building) {
      return NextResponse.json(
        { message: "Building not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: "Building updated successfully",
      building: building.toObject()
    });

  } catch (error) {
    console.error("Error updating building:", error);
    
    if (error.code === 11000) {
      return NextResponse.json(
        { message: "A building with this project title already exists" },
        { status: 409 }
      );
    }
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return NextResponse.json(
        { message: "Validation error", errors: validationErrors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error", error: error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/buildings/[id] - Delete building
 */
export async function DELETE(request, { params }) {
  try {
    const session = await auth();
    
    // if (!session || !session.user) {
    //   return NextResponse.json(
    //     { message: "Authentication required" },
    //     { status: 401 }
    //   );
    // }

    // Check if user has admin role for deletion
    if (session.user.role !== 'admin') {
      return NextResponse.json(
        { message: "Admin access required for deletion" },
        { status: 403 }
      );
    }

    await dbConnect();

    const { id } = params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { message: "Invalid building ID" },
        { status: 400 }
      );
    }

    const building = await Building.findByIdAndDelete(id);

    if (!building) {
      return NextResponse.json(
        { message: "Building not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: "Building deleted successfully",
      deletedBuilding: building.toObject()
    });

  } catch (error) {
    console.error("Error deleting building:", error);
    return NextResponse.json(
      { message: "Internal server error", error: error.message },
      { status: 500 }
    );
  }
}
