'use client'
import { settings } from '@/libs/siteSettings'
import Image from 'next/image'
import React from 'react'
import Link from 'next/link'

export default function BuildingsList({data}) {
    // console.log('BuildingsList:',data)
  return (
    <div className='Buildings-List flex relative top-20 w-full h-[calc(100%-110px)] md:px-20 px-5'>
      <div className='flex w-full overflow-y-auto flex-wrap'>
        {data?.map((i,index)=>
          <div className='flex flex-col md:p-4 md:h-2/3 md:w-1/3 w-full h-4/5' key={index}>
            {/* {console.log(i)} */}
            <Link href={`/projects/${i?._id}`} className='flex flex-col w-full h-full items-center justify-center'>
              <div className='flex relative w-full flex-2/3 items-center justify-center shadow rounded-md overflow-hidden'>
                {i?.renders?.[0]?.url && <Image className='hover:scale-105 ease-linear duration-300 brightness-90 hover:brightness-110 object-cover' src={i?.renders?.[0]?.url} alt='renders' fill/>}
              </div>
              <div className='flex flex-col w-full flex-1/3 items-start py-4 gap-1'>
                <h1 className='text-lg font-medium'>{i?.buildingTitle}</h1>
                <p className='text-sm capitalize'>{i?.buildingType}</p>
                <p className='text-sm capitalize'>{`${i?.buildingSummary?.length*i?.buildingSummary?.width}m2`}</p>
              </div>
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}
