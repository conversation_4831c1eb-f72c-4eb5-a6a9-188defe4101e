'use client'
import { useRef, useEffect, useState } from 'react'
import ExperienceModelAR from './ExperienceModelAR'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { useThree } from '@react-three/fiber'
import { degToRad } from 'three/src/math/MathUtils'
import { useXRSessionManager } from '@/hooks/useXRSessionManager'

export default function ExperienceAR({data}) {
  const refModelAR=useRef(null)
  const refSessionAR=useRef(null)
  const refController=useRef(null)
  const refReticle=useRef(null)
  const {gl}=useThree()
  const glSession=useRef(null)
  const hitTestSource=useRef(null)
  const [errorMessage, setErrorMessage] = useState(null)
  const [showError, setShowError] = useState(false)
  const [showModel, setShowModel] = useState(false)

  // XR Session Manager for centralized session control
  const xrManager = useXRSessionManager()

  useEffect(() => {
    refController.current=gl.xr.getController(0)

    const onSessionStart = async () => {
      console.log('sessionstart')
      glSession.current=gl.xr.getSession()
      if(glSession.current){
        try {
          const viewerReferenceSpace=await glSession.current.requestReferenceSpace('viewer')
          hitTestSource.current=await glSession.current.requestHitTestSource({space:viewerReferenceSpace})

          // Register with XR manager
          xrManager.setActiveSession(glSession.current)
          xrManager.setHitTestSource(hitTestSource.current)

          console.log('Hit test source created successfully')

          const onXRFrame = (_time, frame) => {
            if(!frame || !hitTestSource.current) return

            try {
              const hitTestResults=frame.getHitTestResults(hitTestSource.current)
              if(hitTestResults.length > 0 && refReticle.current){
                const hit=hitTestResults[0]
                const referenceLocalSpace=gl.xr.getReferenceSpace()
                if (referenceLocalSpace) {
                  const hitPose=hit.getPose(referenceLocalSpace)
                  if (hitPose && refReticle.current) {
                    console.log('Hit test successful, updating reticle position')
                    refReticle.current.matrix.fromArray(hitPose.transform.matrix)
                    refReticle.current.visible = true
                  }
                }
              } else if (refReticle.current) {
                refReticle.current.visible = false
              }
            } catch (error) {
              console.error('Error in XR frame processing:', error)
            }
          }
          gl.xr.setAnimationLoop(onXRFrame)
        } catch (error) {
          console.error('Error setting up hit test source:', error)
          setErrorMessage('Failed to initialize AR hit testing')
          setShowError(true)
        }
      }
    }

    const onSessionEnd = () => {
      console.log('sessionend')
      if (refReticle.current) {
        refReticle.current.visible = false
      }
      // Clean up hit test source
      if (hitTestSource.current) {
        hitTestSource.current = null
      }
      // Clear animation loop
      gl.xr.setAnimationLoop(null)
    }

    gl.xr.addEventListener('sessionstart', onSessionStart)
    gl.xr.addEventListener('sessionend', onSessionEnd)

    return () => {
      gl.xr.removeEventListener('sessionstart', onSessionStart)
      gl.xr.removeEventListener('sessionend', onSessionEnd)
      if (gl.xr.getSession()) {
        gl.xr.setAnimationLoop(null)
      }
      // Clean up hit test source on unmount
      if (hitTestSource.current) {
        hitTestSource.current = null
      }
    }
  }, [gl])
  
  const onSelect = () => {
    console.log('Controller selected')
    if (refModelAR.current && refReticle.current) {
      refModelAR.current.position.setFromMatrixPosition(refReticle.current.matrix)
    }
    setShowModel(true)
  }
  
  const addControllerListener = () => {
    refController.current?.addEventListener('select', onSelect)
  }
  
  const removeControllerListener = () => {
    refController.current?.removeEventListener('select', onSelect)
  }
  
  const startAR = async () => {
    refSessionAR.current=await navigator.xr.requestSession('immersive-ar',{requiredFeatures:['hit-test','dom-overlay'],domOverlay:{root:document.body}})
    if(gl && refSessionAR.current){ // Show the model when the controller is selected
      console.log('ar session started')
      gl.xr.enabled=true
      gl.xr.setReferenceSpaceType('local')
      await gl.xr.setSession(refSessionAR.current)
      addControllerListener()
    }
  }
  
  const endAR = async () => {
    console.log('Ending AR session...')

    // Hide reticle
    if (refReticle.current) {
      refReticle.current.visible = false
    }

    // Use XR manager for proper cleanup
    await xrManager.terminateSession()

    // Clear local references
    if (hitTestSource.current) {
      hitTestSource.current = null
    }

    if (refSessionAR.current) {
      refSessionAR.current = null
    }

    // Remove controller listener
    removeControllerListener()

    // Reset model visibility
    setShowModel(false)

    console.log('AR session ended successfully')
  }

  useEffect(() => {
    const checkARSupport = async () => {
      try {
        const supported = await navigator.xr.isSessionSupported('immersive-ar')
        if (!supported) {
          setErrorMessage('AR is not supported in this browser')
          setShowError(true)
        }
      } catch (error) {
        console.log(error)
        setErrorMessage('AR is not supported in this browser')
        setShowError(true)
      }
    }

    checkARSupport()

    startAR()
    addControllerListener() // Add controller listener after starting AR session

    return () => {
      endAR()
      removeControllerListener() // Remove controller listener before ending AR session
    }
  }, [])
  
  console.log('ExperienceAR:',refReticle.current?.position,refReticle.current?.visible)
  return (
    <>
      <mesh
        ref={refReticle}
        matrixAutoUpdate={false}
        visible={false}
        rotation-x={degToRad(-90)}
      >
        <ringGeometry args={[0.15, 0.2, 32]}/>
        <meshBasicMaterial
          color="#ffffff"
          transparent={true}
          opacity={0.8}
          side={2}
        />
      </mesh>
      {showError ? (
        <div className="absolute top-4 right-4 bg-red-600 text-white p-4 rounded-lg z-50 max-w-md">
          <h3 className="font-bold text-lg mb-2">AR Error</h3>
          <p className="text-sm mb-2">
            {errorMessage}
          </p>
          <button
            onClick={() => setShowError(false)}
            className="bg-red-800 hover:bg-red-900 px-3 py-1 rounded text-sm"
          >
            Retry
          </button>
        </div>
      ) : (
        showModel && <ExperienceModelAR data={data} refModelAR={refModelAR}/>
      )}
    </>
  )
}
