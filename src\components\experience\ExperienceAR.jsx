'use client'
import { useRef, useEffect, useState } from 'react'
import ExperienceModelAR from './ExperienceModelAR'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { useThree } from '@react-three/fiber'
import { degToRad } from 'three/src/math/MathUtils'

export default function ExperienceAR({data}) {
  const refModelAR=useRef(null)
  const refSessionAR=useRef(null)
  const refController=useRef(null)
  const refReticle=useRef(null)
  const {gl}=useThree()
  const glSession=useRef(null)
  const [errorMessage, setErrorMessage] = useState(null)
  const [showError, setShowError] = useState(false)
  const [showModel, setShowModel] = useState(false)

  useEffect(() => {
    refController.current=gl.xr.getController(0)

    const onSessionStart = async () => {
      console.log('sessionstart')
      glSession.current=gl.xr.getSession()
      if(glSession.current){
        const viewerReferenceSpace=await glSession.current.requestReferenceSpace('viewer')
        const hitTestSource=await glSession.current.requestHitTestSource({space:viewerReferenceSpace})

        const onXRFrame = (time, frame) => {
          if(!frame || !hitTestSource) return
          const hitTestResults=frame.getHitTestResults(hitTestSource)
          if(hitTestResults.length > 0 && refReticle.current){
            const hit=hitTestResults[0]
            const referenceLocalSpace=gl.xr.getReferenceSpace()
            if (referenceLocalSpace) {
              const hitPose=hit.getPose(referenceLocalSpace)
              if (hitPose && refReticle.current) {
                // console.log('hits available',hitPose.transform.matrix)
                refReticle.current.matrix.fromArray(hitPose.transform.matrix)
                refReticle.current.visible = true
              }
            }
          } else if (refReticle.current) {
            refReticle.current.visible = false
          }
        }
        gl.xr.setAnimationLoop(onXRFrame)
      }
    }

    const onSessionEnd = () => {
      console.log('sessionend')
      if (refReticle.current) {
        refReticle.current.visible = false
      }
    }

    gl.xr.addEventListener('sessionstart', onSessionStart)
    gl.xr.addEventListener('sessionend', onSessionEnd)

    return () => {
      gl.xr.removeEventListener('sessionstart', onSessionStart)
      gl.xr.removeEventListener('sessionend', onSessionEnd)
      if (gl.xr.getSession()) {
        gl.xr.setAnimationLoop(null)
      }
    }
  }, [gl])
  
  const onSelect = () => {
    console.log('Controller selected')
    if (refModelAR.current && refReticle.current) {
      refModelAR.current.position.setFromMatrixPosition(refReticle.current.matrix)
    }
    setShowModel(true)
  }
  
  const addControllerListener = () => {
    refController.current?.addEventListener('select', onSelect)
  }
  
  const removeControllerListener = () => {
    refController.current?.removeEventListener('select', onSelect)
  }
  
  const startAR = async () => {
    refSessionAR.current=await navigator.xr.requestSession('immersive-ar',{requiredFeatures:['hit-test','dom-overlay'],domOverlay:{root:document.body}})
    if(gl && refSessionAR.current){ // Show the model when the controller is selected
      console.log('ar session started')
      gl.xr.enabled=true
      gl.xr.setReferenceSpaceType('local')
      await gl.xr.setSession(refSessionAR.current)
      addControllerListener()
    }
  }
  
  const endAR = async () => {
    if (refSessionAR.current) { // End the session if it exists
      refSessionAR.current.end()
    }
    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT)
    gl.setAnimationLoop(null)
    removeControllerListener()
  }

  useEffect(() => {
    const checkARSupport = async () => {
      try {
        const supported = await navigator.xr.isSessionSupported('immersive-ar')
        if (!supported) {
          setErrorMessage('AR is not supported in this browser')
          setShowError(true)
        }
      } catch (error) {
        console.log(error)
        setErrorMessage('AR is not supported in this browser')
        setShowError(true)
      }
    }

    checkARSupport()

    startAR()
    addControllerListener() // Add controller listener after starting AR session

    return () => {
      endAR()
      removeControllerListener() // Remove controller listener before ending AR session
    }
  }, [])
  
  console.log('ExperienceAR:',refReticle.current?.position,refReticle.current?.visible)
  return (
    <>
      <mesh
        ref={refReticle}
        matrixAutoUpdate={false}
        visible={false}
        rotation-x={degToRad(90)}
      >
        <ringGeometry args={[.5,.65,32]}/>
        <meshBasicMaterial/>
      </mesh>
      {showError ? (
        <div className="absolute top-4 right-4 bg-red-600 text-white p-4 rounded-lg z-50 max-w-md">
          <h3 className="font-bold text-lg mb-2">AR Error</h3>
          <p className="text-sm mb-2">
            {errorMessage}
          </p>
          <button
            onClick={() => setShowError(false)}
            className="bg-red-800 hover:bg-red-900 px-3 py-1 rounded text-sm"
          >
            Retry
          </button>
        </div>
      ) : (
        showModel && <ExperienceModelAR data={data} refModelAR={refModelAR}/>
      )}
    </>
  )
}
