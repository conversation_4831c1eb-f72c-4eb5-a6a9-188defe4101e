'use client'
import { useRef, useEffect, useState } from 'react'
import ExperienceModelAR from './ExperienceModelAR'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { useThree } from '@react-three/fiber'
import { degToRad } from 'three/src/math/MathUtils'
export default function ExperienceAR({data}) {
  const refModelAR=useRef(null)
  const refSessionAR=useRef(null)
  const refController=useRef(null)
  const refReticle=useRef(null)
  const {gl}=useThree()
  const glSession=useRef(null)
  const hitTestSource=useRef(null)
  const currentHitPose=useRef(null)
  const [errorMessage, setErrorMessage] = useState(null)
  const [showError, setShowError] = useState(false)
  const [showModel, setShowModel] = useState(false)

  useEffect(() => {
    refController.current=gl.xr.getController(0)

    const onSessionStart = async () => {
      console.log('sessionstart')
      glSession.current=gl.xr.getSession()
      if(glSession.current){
        try {
          const viewerReferenceSpace=await glSession.current.requestReferenceSpace('viewer')
          hitTestSource.current=await glSession.current.requestHitTestSource({space:viewerReferenceSpace})

          console.log('Hit test source created successfully')

          const onXRFrame = (_time, frame) => {
            if(!frame || !hitTestSource.current) {
              console.log('XR Frame: Missing frame or hit test source')
              return
            }

            if (!refReticle.current) {
              console.log('XR Frame: Missing reticle ref')
              return
            }

            try {
              const hitTestResults=frame.getHitTestResults(hitTestSource.current)
              console.log('Hit test results:', hitTestResults.length)

              if(hitTestResults.length > 0){
                const hit=hitTestResults[0]
                const referenceLocalSpace=gl.xr.getReferenceSpace()
                if (referenceLocalSpace) {
                  const hitPose=hit.getPose(referenceLocalSpace)
                  if (hitPose) {
                    // Store the current hit pose for model placement
                    currentHitPose.current = hitPose

                    // Update reticle position using matrix
                    refReticle.current.matrix.fromArray(hitPose.transform.matrix)
                    refReticle.current.matrixAutoUpdate = false
                    refReticle.current.visible = true

                    console.log('✅ Reticle positioned and visible at:', refReticle.current.position)
                  }
                }
              } else {
                // Hide reticle when no hit test results
                if (refReticle.current) {
                  refReticle.current.visible = false
                  console.log('❌ Reticle hidden - no hit test results')
                }
                currentHitPose.current = null
              }
            } catch (error) {
              console.error('Error in XR frame processing:', error)
              if (refReticle.current) {
                refReticle.current.visible = false
              }
            }
          }
          gl.xr.setAnimationLoop(onXRFrame)
        } catch (error) {
          console.error('Error setting up hit test source:', error)
          setErrorMessage('Failed to initialize AR hit testing')
          setShowError(true)
        }
      }
    }

    const onSessionEnd = () => {
      console.log('sessionend')
      // Reset all states
      setShowModel(false)
      currentHitPose.current = null

      // Hide reticle
      if (refReticle.current) {
        refReticle.current.visible = false
      }

      // Clean up hit test source
      if (hitTestSource.current) {
        hitTestSource.current = null
      }
      // Clear animation loop
      gl.xr.setAnimationLoop(null)
    }

    gl.xr.addEventListener('sessionstart', onSessionStart)
    gl.xr.addEventListener('sessionend', onSessionEnd)

    return () => {
      gl.xr.removeEventListener('sessionstart', onSessionStart)
      gl.xr.removeEventListener('sessionend', onSessionEnd)
      if (gl.xr.getSession()) {
        gl.xr.setAnimationLoop(null)
      }
      // Clean up hit test source on unmount
      if (hitTestSource.current) {
        hitTestSource.current = null
      }
    }
  }, [gl])
  
  const onSelect = () => {
    console.log('Controller selected - attempting model placement')

    try {
      // Check if we have a valid hit pose and reticle
      if (currentHitPose.current && refReticle.current && refReticle.current.visible) {
        console.log('Valid hit pose found, placing model...')

        // Hide the reticle immediately
        refReticle.current.visible = false

        // Show the model
        setShowModel(true)

        // Set model position from reticle position
        setTimeout(() => {
          if (refModelAR.current && refReticle.current) {
            refModelAR.current.position.setFromMatrixPosition(refReticle.current.matrix)
            refModelAR.current.visible = true
            console.log('Model placed at:', refModelAR.current.position)
          }
        }, 100)

      } else {
        console.warn('No valid hit pose available for model placement')
        setErrorMessage('Please aim at a surface before placing the model')
        setShowError(true)
      }
    } catch (error) {
      console.error('Error during model placement:', error)
      setErrorMessage('Failed to place model: ' + error.message)
      setShowError(true)
    }
  }
  
  const addControllerListener = () => {
    refController.current?.addEventListener('select', onSelect)
  }
  
  const removeControllerListener = () => {
    refController.current?.removeEventListener('select', onSelect)
  }

  const resetModelPlacement = () => {
    console.log('Resetting model placement')
    setShowModel(false)
    currentHitPose.current = null
    if (refModelAR.current) {
      refModelAR.current.visible = false
    }
    if (refReticle.current) {
      refReticle.current.visible = false
    }
  }
  
  const startAR = async () => {
    try {
      console.log('🚀 Starting AR session...')
      refSessionAR.current=await navigator.xr.requestSession('immersive-ar',{requiredFeatures:['hit-test','dom-overlay'],domOverlay:{root:document.body}})

      if(gl && refSessionAR.current){
        console.log('✅ AR session created successfully')
        gl.xr.enabled=true
        gl.xr.setReferenceSpaceType('local')
        await gl.xr.setSession(refSessionAR.current)
        addControllerListener()
        console.log('✅ AR session setup complete')
      }
    } catch (error) {
      console.error('❌ Error starting AR session:', error)
      setErrorMessage('Failed to start AR session: ' + error.message)
      setShowError(true)
    }
  }
  
  const endAR = async () => {
    console.log('Ending AR session...')

    // Hide reticle
    if (refReticle.current) {
      refReticle.current.visible = false
    }

    // End XR session
    if (refSessionAR.current) {
      try {
        await refSessionAR.current.end()
      } catch (error) {
        console.error('Error ending AR session:', error)
      }
      refSessionAR.current = null
    }

    // Clear local references
    if (hitTestSource.current) {
      hitTestSource.current = null
    }

    // Clear animation loop
    gl.xr.setAnimationLoop(null)
    gl.xr.enabled = false

    // Remove controller listener
    removeControllerListener()

    // Reset model visibility
    setShowModel(false)

    console.log('AR session ended successfully')
  }

  useEffect(() => {
    const checkARSupport = async () => {
      try {
        const supported = await navigator.xr.isSessionSupported('immersive-ar')
        if (!supported) {
          setErrorMessage('AR is not supported in this browser')
          setShowError(true)
        }
      } catch (error) {
        console.log(error)
        setErrorMessage('AR is not supported in this browser')
        setShowError(true)
      }
    }

    checkARSupport()

    startAR()
    addControllerListener() // Add controller listener after starting AR session

    return () => {
      endAR()
      removeControllerListener() // Remove controller listener before ending AR session
    }
  }, [])
  
  console.log('ExperienceAR - Model visible:', showModel, 'Reticle ref:', !!refReticle.current)
  return (
    <>
      {/* Debug: Always visible test reticle */}
      <mesh
        position={[0, 0, -2]}
        rotation-x={degToRad(-90)}
        visible={true}
      >
        <ringGeometry args={[0.05, 0.08, 16]}/>
        <meshBasicMaterial
          color="#ff0000"
          transparent={true}
          opacity={0.5}
        />
      </mesh>

      {/* Main AR reticle */}
      <mesh
        ref={refReticle}
        matrixAutoUpdate={false}
        visible={false}
        rotation-x={degToRad(-90)}
        position={[0, 0, -1]}
      >
        <ringGeometry args={[0.1, 0.15, 32]}/>
        <meshBasicMaterial
          color="#00ff00"
          transparent={true}
          opacity={1.0}
          side={2}
          depthTest={false}
          depthWrite={false}
        />
      </mesh>
      {showError ? (
        <div className="absolute top-4 right-4 bg-red-600 text-white p-4 rounded-lg z-50 max-w-md">
          <h3 className="font-bold text-lg mb-2">AR Error</h3>
          <p className="text-sm mb-2">
            {errorMessage}
          </p>
          <button
            onClick={() => setShowError(false)}
            className="bg-red-800 hover:bg-red-900 px-3 py-1 rounded text-sm"
          >
            Retry
          </button>
        </div>
      ) : (
        <>
          {showModel && <ExperienceModelAR data={data} refModelAR={refModelAR}/>}
          {showModel && (
            <div className="absolute bottom-4 right-4 z-50">
              <button
                onClick={resetModelPlacement}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm"
              >
                Reset Model
              </button>
            </div>
          )}
        </>
      )}
    </>
  )
}
