'use client'
import { useRef, useEffect, useState } from 'react'
import ExperienceModelAR from './ExperienceModelAR'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { useThree } from '@react-three/fiber'
import { degToRad } from 'three/src/math/MathUtils'
import { useXRSessionManager } from '@/hooks/useXRSessionManager'
import * as THREE from 'three'

export default function ExperienceAR({data}) {
  const refModelAR=useRef(null)
  const refSessionAR=useRef(null)
  const refController=useRef(null)
  const refReticle=useRef(null)
  const {gl}=useThree()
  const glSession=useRef(null)
  const hitTestSource=useRef(null)
  const currentHitPose=useRef(null)
  const [errorMessage, setErrorMessage] = useState(null)
  const [showError, setShowError] = useState(false)
  const [showModel, setShowModel] = useState(false)
  const [reticleVisible, setReticleVisible] = useState(false)

  // XR Session Manager for centralized session control
  const xrManager = useXRSessionManager()

  useEffect(() => {
    refController.current=gl.xr.getController(0)

    const onSessionStart = async () => {
      console.log('sessionstart')
      glSession.current=gl.xr.getSession()
      if(glSession.current){
        try {
          const viewerReferenceSpace=await glSession.current.requestReferenceSpace('viewer')
          hitTestSource.current=await glSession.current.requestHitTestSource({space:viewerReferenceSpace})

          // Register with XR manager
          xrManager.setActiveSession(glSession.current)
          xrManager.setHitTestSource(hitTestSource.current)

          console.log('Hit test source created successfully')

          const onXRFrame = (_time, frame) => {
            if(!frame || !hitTestSource.current || !refReticle.current) return

            try {
              const hitTestResults=frame.getHitTestResults(hitTestSource.current)
              if(hitTestResults.length > 0){
                const hit=hitTestResults[0]
                const referenceLocalSpace=gl.xr.getReferenceSpace()
                if (referenceLocalSpace) {
                  const hitPose=hit.getPose(referenceLocalSpace)
                  if (hitPose) {
                    // Store the current hit pose for model placement
                    currentHitPose.current = hitPose

                    // Update reticle position using matrix
                    refReticle.current.matrix.fromArray(hitPose.transform.matrix)
                    refReticle.current.matrixAutoUpdate = false

                    // Make reticle visible
                    if (!reticleVisible) {
                      setReticleVisible(true)
                      console.log('Reticle now visible - hit test successful')
                    }
                  }
                }
              } else {
                // Hide reticle when no hit test results
                if (reticleVisible) {
                  setReticleVisible(false)
                  console.log('Reticle hidden - no hit test results')
                }
                currentHitPose.current = null
              }
            } catch (error) {
              console.error('Error in XR frame processing:', error)
              if (reticleVisible) {
                setReticleVisible(false)
              }
            }
          }
          gl.xr.setAnimationLoop(onXRFrame)
        } catch (error) {
          console.error('Error setting up hit test source:', error)
          setErrorMessage('Failed to initialize AR hit testing')
          setShowError(true)
        }
      }
    }

    const onSessionEnd = () => {
      console.log('sessionend')
      // Reset all states
      setReticleVisible(false)
      setShowModel(false)
      currentHitPose.current = null

      // Clean up hit test source
      if (hitTestSource.current) {
        hitTestSource.current = null
      }
      // Clear animation loop
      gl.xr.setAnimationLoop(null)
    }

    gl.xr.addEventListener('sessionstart', onSessionStart)
    gl.xr.addEventListener('sessionend', onSessionEnd)

    return () => {
      gl.xr.removeEventListener('sessionstart', onSessionStart)
      gl.xr.removeEventListener('sessionend', onSessionEnd)
      if (gl.xr.getSession()) {
        gl.xr.setAnimationLoop(null)
      }
      // Clean up hit test source on unmount
      if (hitTestSource.current) {
        hitTestSource.current = null
      }
    }
  }, [gl])
  
  const onSelect = () => {
    console.log('Controller selected - attempting model placement')

    try {
      // Check if we have a valid hit pose
      if (currentHitPose.current && reticleVisible) {
        console.log('Valid hit pose found, placing model...')

        // Get the position from the current hit pose
        const hitMatrix = new Float32Array(currentHitPose.current.transform.matrix)
        const position = new THREE.Vector3()
        const quaternion = new THREE.Quaternion()
        const scale = new THREE.Vector3()

        // Decompose the matrix to get position
        const matrix = new THREE.Matrix4().fromArray(hitMatrix)
        matrix.decompose(position, quaternion, scale)

        console.log('Placing model at position:', position)

        // Hide the reticle immediately
        setReticleVisible(false)

        // Show the model
        setShowModel(true)

        // Set model position immediately if available, otherwise use timeout
        if (refModelAR.current) {
          refModelAR.current.position.copy(position)
          refModelAR.current.visible = true
          console.log('Model placed immediately at:', refModelAR.current.position)
        } else {
          // Wait for model to be available and set its position
          let attempts = 0
          const maxAttempts = 10
          const checkModel = () => {
            attempts++
            if (refModelAR.current) {
              refModelAR.current.position.copy(position)
              refModelAR.current.visible = true
              console.log('Model placed after', attempts, 'attempts at:', refModelAR.current.position)
            } else if (attempts < maxAttempts) {
              setTimeout(checkModel, 50)
            } else {
              console.error('Model ref not available after maximum attempts')
              setErrorMessage('Failed to place model - model not loaded')
              setShowError(true)
            }
          }
          checkModel()
        }

      } else {
        console.warn('No valid hit pose available for model placement. Reticle visible:', reticleVisible)
        setErrorMessage('Please aim at a surface before placing the model')
        setShowError(true)
      }
    } catch (error) {
      console.error('Error during model placement:', error)
      setErrorMessage('Failed to place model: ' + error.message)
      setShowError(true)
    }
  }
  
  const addControllerListener = () => {
    refController.current?.addEventListener('select', onSelect)
  }
  
  const removeControllerListener = () => {
    refController.current?.removeEventListener('select', onSelect)
  }

  const resetModelPlacement = () => {
    console.log('Resetting model placement')
    setShowModel(false)
    setReticleVisible(false)
    currentHitPose.current = null
    if (refModelAR.current) {
      refModelAR.current.visible = false
    }
  }
  
  const startAR = async () => {
    refSessionAR.current=await navigator.xr.requestSession('immersive-ar',{requiredFeatures:['hit-test','dom-overlay'],domOverlay:{root:document.body}})
    if(gl && refSessionAR.current){ // Show the model when the controller is selected
      console.log('ar session started')
      gl.xr.enabled=true
      gl.xr.setReferenceSpaceType('local')
      await gl.xr.setSession(refSessionAR.current)
      addControllerListener()
    }
  }
  
  const endAR = async () => {
    console.log('Ending AR session...')

    // Hide reticle
    if (refReticle.current) {
      refReticle.current.visible = false
    }

    // Use XR manager for proper cleanup
    await xrManager.terminateSession()

    // Clear local references
    if (hitTestSource.current) {
      hitTestSource.current = null
    }

    if (refSessionAR.current) {
      refSessionAR.current = null
    }

    // Remove controller listener
    removeControllerListener()

    // Reset model visibility
    setShowModel(false)

    console.log('AR session ended successfully')
  }

  useEffect(() => {
    const checkARSupport = async () => {
      try {
        const supported = await navigator.xr.isSessionSupported('immersive-ar')
        if (!supported) {
          setErrorMessage('AR is not supported in this browser')
          setShowError(true)
        }
      } catch (error) {
        console.log(error)
        setErrorMessage('AR is not supported in this browser')
        setShowError(true)
      }
    }

    checkARSupport()

    startAR()
    addControllerListener() // Add controller listener after starting AR session

    return () => {
      endAR()
      removeControllerListener() // Remove controller listener before ending AR session
    }
  }, [])
  
  console.log('ExperienceAR - Reticle visible:', reticleVisible, 'Model visible:', showModel)
  return (
    <>
      <mesh
        ref={refReticle}
        matrixAutoUpdate={false}
        visible={reticleVisible && !showModel}
        rotation-x={degToRad(-90)}
      >
        <ringGeometry args={[0.1, 0.15, 32]}/>
        <meshBasicMaterial
          color="#00ff00"
          transparent={true}
          opacity={0.9}
          side={2}
          depthTest={false}
          depthWrite={false}
        />
      </mesh>
      {showError ? (
        <div className="absolute top-4 right-4 bg-red-600 text-white p-4 rounded-lg z-50 max-w-md">
          <h3 className="font-bold text-lg mb-2">AR Error</h3>
          <p className="text-sm mb-2">
            {errorMessage}
          </p>
          <button
            onClick={() => setShowError(false)}
            className="bg-red-800 hover:bg-red-900 px-3 py-1 rounded text-sm"
          >
            Retry
          </button>
        </div>
      ) : (
        <>
          {showModel && <ExperienceModelAR data={data} refModelAR={refModelAR}/>}
          {showModel && (
            <div className="absolute bottom-4 right-4 z-50">
              <button
                onClick={resetModelPlacement}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm"
              >
                Reset Model
              </button>
            </div>
          )}
        </>
      )}
    </>
  )
}
