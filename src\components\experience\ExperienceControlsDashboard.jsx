'use client'
import React, { useState, useRef, useEffect } from 'react';
import { OrbitControls } from '@react-three/drei'
import { degToRad } from 'three/src/math/MathUtils'
import { useThree } from '@react-three/fiber';
import { ref } from 'firebase/storage';
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext';

export default function ExperienceControls({data}) {
  const {experienceState,experienceDispatch}=useExperienceContext()
  const [target, setTarget]=useState(null);
  const [snapObject, setSnapObject]=useState({});
  const [enableComntrols, setEnableComntrols]=useState(true);
  const refControls=useRef(null)
  const {camera,scene}=useThree()

  const snap = (targetPosition, targetLookAt) => {
    if (refControls.current) {
      // Set the camera's new position
      camera.position.copy(targetPosition);

      // Make the camera look at the targetLookAt point
      camera.lookAt(targetLookAt);

      // Update OrbitControls' target to match the camera's new lookAt point
      // This is crucial for OrbitControls to behave correctly after the snap
      refControls.current.target.copy(targetLookAt);

      // Optional: Smoothly transition if you want
      // You might need a more sophisticated tweening library for very smooth transitions
      // For instant snap, just the above is fine.

      // Update controls to reflect the changes
      refControls.current.update();
    }
  };

  useEffect(() => {
    if (experienceState?.activeRoomSnap && scene) {
      scene.getObjectByName(experienceState?.activeRoomSnap).traverse((child) => {
        if (child.isMesh) {
          child.name = experienceState?.activeRoomSnap;
          setSnapObject(child)
        }
      });
      if (snapObject &&camera) {
        snap(snapObject?.position, snapObject?.position)
      }
    }
  }, [scene,experienceState?.activeRoomSnap]); 
  
  // console.log('ExperienceControls:',experienceState)
  return (
    <OrbitControls
      ref={refControls}
      minDistance={experienceState?.firstPersonView ? 0 : data?.minDistance}
      maxDistance={experienceState?.firstPersonView ? .5 :  data?.maxDistance}
      maxPolarAngle={experienceState?.firstPersonView ? degToRad(135) : degToRad(85)}
      minPolarAngle={experienceState?.firstPersonView ? degToRad(45) :  degToRad(0)}
      rotateSpeed={-.25}
      enabled={enableComntrols}
      enablePan={false}
    />
  )
}
