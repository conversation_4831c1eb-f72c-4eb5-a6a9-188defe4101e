'use client';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { OrbitControls } from '@react-three/drei';
import { degToRad } from 'three/src/math/MathUtils';
import { useThree, useFrame } from '@react-three/fiber';
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext';
import { useCameraSnapTracking } from '@/hooks/useCameraSnapTracking';
import * as THREE from 'three';

export default function ExperienceControlsImproved({ data }) {
  const { experienceState, experienceDispatch } = useExperienceContext();
  const [controlsEnabled, setControlsEnabled] = useState(true);
  const refControls = useRef(null);
  const { camera, scene } = useThree();
  
  // Use custom hook for camera snap tracking
  const {
    isSnapping,
    hasObjectChanged,
    getTrackedObject,
    initializeSnap,
    updateSnapAnimation,
    clearTracking,
    calculateCameraPosition
  } = useCameraSnapTracking();

  /**
   * Smoothly snap camera to target object
   */
  const snapToTarget = useCallback((targetObject, customOffset) => {
    if (!refControls.current || !targetObject || !camera) {
      console.warn("snapToTarget: Missing required parameters");
      return;
    }

    // Default camera offset (behind and slightly above the object)
    const defaultOffset = new THREE.Vector3(0, 1, 2);
    const offset = customOffset || defaultOffset;

    // Initialize the snap operation
    const success = initializeSnap(targetObject, camera, refControls.current, offset);
    
    if (success) {
      setControlsEnabled(false);
    }
  }, [camera, initializeSnap]);

  /**
   * Handle activeRoomSnap changes
   */
  useEffect(() => {
    if (experienceState?.activeRoomSnap && scene) {
      const foundObject = scene.getObjectByName(experienceState.activeRoomSnap);
      if (foundObject) {
        // For room snaps, use a closer camera position for first-person view
        const firstPersonOffset = new THREE.Vector3(0, 0.2, 0.1);
        const thirdPersonOffset = new THREE.Vector3(0, 1, 2);
        
        const offset = experienceState?.firstPersonView ? firstPersonOffset : thirdPersonOffset;
        snapToTarget(foundObject, offset);
      } else {
        console.warn(`Object with name "${experienceState.activeRoomSnap}" not found in the scene.`);
      }
    } else {
      // Clear tracking when no active snap
      clearTracking();
      if (isSnapping()) {
        setControlsEnabled(true);
      }
    }
  }, [scene, experienceState?.activeRoomSnap, experienceState?.firstPersonView, snapToTarget, clearTracking, isSnapping]);

  /**
   * Main animation loop - handles smooth transitions and object tracking
   */
  useFrame((state, delta) => {
    const controls = refControls.current;
    if (!controls) return;

    // Handle snap animation
    if (isSnapping()) {
      const snapCompleted = updateSnapAnimation(delta, camera, controls);
      
      if (snapCompleted) {
        setControlsEnabled(true);
      }
    }
    // Track object changes when not actively snapping
    else {
      const trackedObject = getTrackedObject();
      if (trackedObject && hasObjectChanged(trackedObject)) {
        console.log("Tracked object has moved/rotated, updating camera position");
        
        // Determine offset based on current view mode
        const firstPersonOffset = new THREE.Vector3(0, 0.2, 0.1);
        const thirdPersonOffset = new THREE.Vector3(0, 1, 2);
        const offset = experienceState?.firstPersonView ? firstPersonOffset : thirdPersonOffset;
        
        snapToTarget(trackedObject, offset);
      }
    }
  });

  /**
   * Cycle through available room snap points
   */
  const cycleSnapPoints = useCallback(() => {
    if (!data?.roomSnaps || data.roomSnaps.length === 0) {
      console.warn("No room snaps available for cycling");
      return;
    }

    const snapNames = data.roomSnaps.map(snap => snap.name);
    const currentIndex = snapNames.findIndex(
      (snapName) => snapName === experienceState?.activeRoomSnap
    );

    const nextIndex = (currentIndex + 1) % snapNames.length;
    const nextSnapName = snapNames[nextIndex];

    experienceDispatch({ type: 'SET_ACTIVE_ROOM_SNAP', payload: nextSnapName });
  }, [data?.roomSnaps, experienceState?.activeRoomSnap, experienceDispatch]);

  /**
   * Manually snap to a specific object by name
   */
  const snapToObjectByName = useCallback((objectName, customOffset) => {
    if (!scene) {
      console.warn("Scene not available for object lookup");
      return;
    }
    
    const foundObject = scene.getObjectByName(objectName);
    if (foundObject) {
      snapToTarget(foundObject, customOffset);
    } else {
      console.warn(`Object with name "${objectName}" not found in the scene`);
    }
  }, [scene, snapToTarget]);

  /**
   * Reset camera to default position
   */
  const resetCamera = useCallback(() => {
    if (!camera || !refControls.current) return;
    
    clearTracking();
    setControlsEnabled(true);
    
    // Reset to default position
    camera.position.set(0, 5, 10);
    refControls.current.target.set(0, 0, 0);
    refControls.current.update();
    
    // Clear active room snap
    experienceDispatch({ type: 'SET_ACTIVE_ROOM_SNAP', payload: null });
  }, [camera, clearTracking, experienceDispatch]);

  // Expose methods for external use
  React.useImperativeHandle(refControls, () => ({
    cycleSnapPoints,
    snapToObjectByName,
    resetCamera,
    isSnapping: isSnapping,
    getTrackedObject
  }), [cycleSnapPoints, snapToObjectByName, resetCamera, isSnapping, getTrackedObject]);

  return (
    <OrbitControls
      ref={refControls}
      minDistance={experienceState?.firstPersonView ? 0.0 : (parseFloat(data?.minDistance) || 1)}
      maxDistance={experienceState?.firstPersonView ? 0.5 : (parseFloat(data?.maxDistance) || 100)}
      maxPolarAngle={experienceState?.firstPersonView ? degToRad(135) : degToRad(85)}
      minPolarAngle={experienceState?.firstPersonView ? degToRad(45) : degToRad(0)}
      rotateSpeed={-0.25}
      enabled={controlsEnabled}
      enablePan={false}
      enableDamping={true}
      dampingFactor={0.05}
    />
  );
}
