'use client'
import { Canvas } from '@react-three/fiber'
import React, { Suspense, useRef } from 'react'
import ExperienceAR from './ExperienceAR'
import Experience360 from './Experience360'
import ExperienceModel from './ExperienceModel'
import ExperienceOrbitControls from './ExperienceOrbitControls'
import CameraControlsErrorBoundary from './CameraControlsErrorBoundary'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import ExperienceCameraControls from './ExperienceCameraControls'
import LoadingSpinner from '../LoadingSpinner'

export default function ExperienceWorld({data}) {
  const {experienceState}=useExperienceContext()
  // console.log('ExperienceWorld:',experienceState?.enableSnapView)
  return (
    <CameraControlsErrorBoundary>
      <Canvas>
        <Suspense fallback={<LoadingSpinner/>}>
          {experienceState?.modeAR
            ? <ExperienceAR data={data}/>
            : <>
                {/* {experienceState?.enableSnapView
                  ? <ExperienceCameraControls/>
                  : <ExperienceOrbitControls data={data}/>
                } */}
                {/* <ExperienceCameraControls data={data}/> */}
                <ExperienceOrbitControls data={data}/>
                {experienceState?.mode360 && <Experience360 data={data}/>}
                {experienceState?.modeModel && <ExperienceModel data={data}/>}
                {/* <CameraDebugWrapper /> */}
              </>
          }
        </Suspense>
      </Canvas>
      {/* <Loader/> */}
    </CameraControlsErrorBoundary>
  )
}