'use client'
import { Canvas } from '@react-three/fiber'
import React, { Suspense, useRef, useEffect } from 'react'
import ExperienceAR from './ExperienceAR'
import Experience360 from './Experience360'
import ExperienceModel from './ExperienceModel'
import ExperienceOrbitControls from './ExperienceOrbitControls'
import CameraControlsErrorBoundary from './CameraControlsErrorBoundary'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import ExperienceCameraControls from './ExperienceCameraControls'
import LoadingSpinner from '../LoadingSpinner'
import { useXRSessionManager } from '@/hooks/useXRSessionManager'

export default function ExperienceWorld({data}) {
  const {experienceState}=useExperienceContext()
  const previousModeAR = useRef(experienceState?.modeAR)
  const xrManager = useXRSessionManager()

  // Handle XR session termination when switching out of AR mode
  useEffect(() => {
    const currentModeAR = experienceState?.modeAR

    // If we were in AR mode and now we're not, terminate the XR session
    if (previousModeAR.current && !currentModeAR) {
      console.log('Switching out of AR mode, terminating XR session...')

      // Use the XR session manager for proper cleanup
      const terminateActiveSession = async () => {
        try {
          if (xrManager.hasActiveSession()) {
            await xrManager.terminateSession()
            console.log('XR session terminated successfully via manager')
          } else {
            // Fallback: Check for any remaining XR sessions
            const canvas = document.querySelector('canvas')
            if (canvas) {
              const gl = canvas.getContext('webgl2') || canvas.getContext('webgl')
              if (gl && gl.xr && gl.xr.getSession()) {
                console.log('Found active XR session, ending it...')
                await gl.xr.getSession().end()
                gl.xr.enabled = false
                gl.xr.setAnimationLoop(null)
                console.log('XR session terminated successfully via fallback')
              }
            }
          }
        } catch (error) {
          console.error('Error terminating XR session:', error)
        }
      }

      terminateActiveSession()
    }

    // Update the previous mode reference
    previousModeAR.current = currentModeAR
  }, [experienceState?.modeAR, xrManager])

  // console.log('ExperienceWorld:',experienceState?.enableSnapView)
  return (
    <CameraControlsErrorBoundary>
      <Canvas>
        <Suspense fallback={<LoadingSpinner/>}>
          {experienceState?.modeAR
            ? <ExperienceAR data={data}/>
            : <>
                {/* {experienceState?.enableSnapView
                  ? <ExperienceCameraControls/>
                  : <ExperienceOrbitControls data={data}/>
                } */}
                {/* <ExperienceCameraControls data={data}/> */}
                <ExperienceOrbitControls data={data}/>
                {experienceState?.mode360 && <Experience360 data={data}/>}
                {experienceState?.modeModel && <ExperienceModel data={data}/>}
                {/* <CameraDebugWrapper /> */}
              </>
          }
        </Suspense>
      </Canvas>
      {/* <Loader/> */}
    </CameraControlsErrorBoundary>
  )
}