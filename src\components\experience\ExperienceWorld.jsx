'use client'
import { Canvas } from '@react-three/fiber'
import React, { Suspense, useRef, useEffect, useState } from 'react'
import ExperienceAR, { ExperienceARUI } from './ExperienceAR'
import Experience360 from './Experience360'
import ExperienceModel from './ExperienceModel'
import ExperienceOrbitControls from './ExperienceOrbitControls'
import CameraControlsErrorBoundary from './CameraControlsErrorBoundary'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import ExperienceCameraControls from './ExperienceCameraControls'
import LoadingSpinner from '../LoadingSpinner'

// New AR System Components
import ARSessionManager from './ARSessionManager'
import ARReticle, { CrosshairReticle, TargetReticle } from './ARReticle'
import ARController, { ARControllerVisual, ARTouchHandler } from './ARController'
import ARObjectManager, { useARObjectManager } from './ARObjectGenerator'
import * as THREE from 'three'
export default function ExperienceWorld({data}) {
  const {experienceState}=useExperienceContext()
  const previousModeAR = useRef(experienceState?.modeAR)

  // Legacy AR state management (for old ExperienceAR component)
  const [errorMessage, setErrorMessage] = useState(null)
  const [showError, setShowError] = useState(false)
  const [showModel, setShowModel] = useState(false)

  // Legacy refs for AR functionality
  const refModelAR = useRef(null)
  const refSessionAR = useRef(null)
  const refController = useRef(null)
  const refReticle = useRef(null)
  const glSession = useRef(null)
  const hitTestSource = useRef(null)
  const currentHitPose = useRef(null)

  // New AR System State
  const [useNewARSystem, setUseNewARSystem] = useState(true)
  const [arSessionData, setArSessionData] = useState(null)
  const [currentHitPose2, setCurrentHitPose2] = useState(null)
  const [objectCount, setObjectCount] = useState(0)
  const [reticleType, setReticleType] = useState('default') // 'default', 'crosshair', 'target'
  const objectManagerRef = useRef(null)

  const resetModelPlacement = () => {
    console.log('Resetting model placement')
    setShowModel(false)
    if (refModelAR.current) {
      refModelAR.current.visible = false
    }
    if (refReticle.current) {
      refReticle.current.visible = false
    }
  }

  // New AR System Handlers
  const handleARSessionStart = (session) => {
    console.log('🚀 New AR session started:', session)
  }

  const handleARSessionEnd = () => {
    console.log('🛑 New AR session ended')
    setCurrentHitPose2(null)
    setObjectCount(0)
  }

  const handleHitTestReady = (hitTestSource) => {
    console.log('🎯 Hit test source ready:', hitTestSource)
  }

  const handleHitPoseUpdate = (hitPose, hit) => {
    setCurrentHitPose2(hitPose)
  }

  const handleControllerSelect = (event, controller) => {
    if (currentHitPose2 && objectManagerRef.current) {
      // Extract position from hit pose
      const matrix = new THREE.Matrix4().fromArray(currentHitPose2.transform.matrix)
      const position = new THREE.Vector3()
      position.setFromMatrixPosition(matrix)

      // Add random object at hit position
      const newObject = objectManagerRef.current.addObject([position.x, position.y, position.z], currentHitPose2)
      setObjectCount(prev => prev + 1)

      console.log('🎲 Placed object at:', position)
    } else {
      console.warn('⚠️ No hit pose available for object placement')
    }
  }

  const handleObjectAdded = (object) => {
    console.log('➕ Object added:', object.id)
  }

  const handleObjectRemoved = (object) => {
    console.log('➖ Object removed:', object.id)
    setObjectCount(prev => Math.max(0, prev - 1))
  }

  const clearAllObjects = () => {
    if (objectManagerRef.current) {
      objectManagerRef.current.clearAllObjects()
      setObjectCount(0)
    }
  }

  const cycleReticleType = () => {
    const types = ['default', 'crosshair', 'target']
    const currentIndex = types.indexOf(reticleType)
    const nextIndex = (currentIndex + 1) % types.length
    setReticleType(types[nextIndex])
    console.log('🎯 Reticle type changed to:', types[nextIndex])
  }

  // Handle XR session termination when switching out of AR mode
  useEffect(() => {
    const currentModeAR = experienceState?.modeAR

    // If we were in AR mode and now we're not, terminate the XR session
    if (previousModeAR.current && !currentModeAR) {
      console.log('Switching out of AR mode, terminating XR session...')

      // Simple fallback cleanup
      const terminateActiveSession = async () => {
        try {
          const canvas = document.querySelector('canvas')
          if (canvas) {
            const gl = canvas.getContext('webgl2') || canvas.getContext('webgl')
            if (gl && gl.xr && gl.xr.getSession()) {
              console.log('Found active XR session, ending it...')
              await gl.xr.getSession().end()
              gl.xr.enabled = false
              gl.xr.setAnimationLoop(null)
              console.log('XR session terminated successfully')
            }
          }
        } catch (error) {
          console.error('Error terminating XR session:', error)
        }
      }

      terminateActiveSession()
    }

    // Update the previous mode reference
    previousModeAR.current = currentModeAR
  }, [experienceState?.modeAR])

  // console.log('ExperienceWorld:',experienceState?.enableSnapView)
  return (
    <CameraControlsErrorBoundary>
      <Canvas>
        <Suspense fallback={<LoadingSpinner/>}>
          {experienceState?.modeAR
            ? (
                useNewARSystem ? (
                  // New AR System
                  <ARSessionManager
                    onSessionStart={handleARSessionStart}
                    onSessionEnd={handleARSessionEnd}
                    onHitTestReady={handleHitTestReady}
                  >
                    {(sessionData) => (
                      <>
                        {/* Lighting for AR objects */}
                        <ambientLight intensity={0.6} />
                        <directionalLight position={[10, 10, 5]} intensity={0.8} />

                        {/* AR Reticle */}
                        {sessionData.hitTestReady && (
                          <>
                            {reticleType === 'default' && (
                              <ARReticle
                                hitTestSource={sessionData.hitTestSource}
                                onHitPoseUpdate={handleHitPoseUpdate}
                                visible={true}
                              />
                            )}
                            {reticleType === 'crosshair' && (
                              <CrosshairReticle
                                hitTestSource={sessionData.hitTestSource}
                                onHitPoseUpdate={handleHitPoseUpdate}
                                visible={true}
                              />
                            )}
                            {reticleType === 'target' && (
                              <TargetReticle
                                hitTestSource={sessionData.hitTestSource}
                                onHitPoseUpdate={handleHitPoseUpdate}
                                visible={true}
                              />
                            )}
                          </>
                        )}

                        {/* AR Controller */}
                        <ARController
                          onSelect={handleControllerSelect}
                          enabled={sessionData.isSessionActive}
                        />

                        {/* AR Controller Visual */}
                        <ARControllerVisual
                          controllerIndex={0}
                          showRay={true}
                          rayColor="#ffffff"
                          rayLength={5}
                        />

                        {/* AR Object Manager */}
                        <primitive object={{
                          ref: (manager) => {
                            if (manager && manager.objectManager) {
                              objectManagerRef.current = manager.objectManager
                            }
                          }
                        }} />
                        <ARObjectManager
                          maxObjects={20}
                          autoRemove={true}
                          autoRemoveDelay={30000}
                          onObjectAdded={handleObjectAdded}
                          onObjectRemoved={handleObjectRemoved}
                        />

                        {/* Touch Handler for mobile */}
                        <ARTouchHandler
                          onTap={handleControllerSelect}
                          enabled={sessionData.isSessionActive}
                        />
                      </>
                    )}
                  </ARSessionManager>
                ) : (
                  // Legacy AR System
                  <ExperienceAR
                    data={data}
                    refModelAR={refModelAR}
                    refSessionAR={refSessionAR}
                    refController={refController}
                    refReticle={refReticle}
                    glSession={glSession}
                    hitTestSource={hitTestSource}
                    currentHitPose={currentHitPose}
                    setErrorMessage={setErrorMessage}
                    setShowError={setShowError}
                    showModel={showModel}
                    setShowModel={setShowModel}
                  />
                )
              )
            : <>
                {/* {experienceState?.enableSnapView
                  ? <ExperienceCameraControls/>
                  : <ExperienceOrbitControls data={data}/>
                } */}
                {/* <ExperienceCameraControls data={data}/> */}
                <ExperienceOrbitControls data={data}/>
                {experienceState?.mode360 && <Experience360 data={data}/>}
                {experienceState?.modeModel && <ExperienceModel data={data}/>}
                {/* <CameraDebugWrapper /> */}
              </>
          }
        </Suspense>
      </Canvas>

      {/* AR UI Components outside Canvas */}
      {experienceState?.modeAR && (
        <>
          {useNewARSystem ? (
            // New AR System UI
            <div className="absolute top-4 right-4 z-50 space-y-2">
              <div className="bg-black/70 text-white p-3 rounded-lg">
                <h3 className="font-bold text-sm mb-2">AR Controls</h3>
                <div className="space-y-2 text-xs">
                  <div>Objects: {objectCount}</div>
                  <div>Reticle: {reticleType}</div>
                  <div>Hit Pose: {currentHitPose2 ? '✅' : '❌'}</div>
                </div>
                <div className="flex gap-2 mt-3">
                  <button
                    onClick={cycleReticleType}
                    className="bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs"
                  >
                    Cycle Reticle
                  </button>
                  <button
                    onClick={clearAllObjects}
                    className="bg-red-600 hover:bg-red-700 px-2 py-1 rounded text-xs"
                  >
                    Clear All
                  </button>
                </div>
                <div className="mt-2">
                  <button
                    onClick={() => setUseNewARSystem(false)}
                    className="bg-gray-600 hover:bg-gray-700 px-2 py-1 rounded text-xs"
                  >
                    Use Legacy AR
                  </button>
                </div>
              </div>
            </div>
          ) : (
            // Legacy AR System UI
            <>
              <ExperienceARUI
                showError={showError}
                errorMessage={errorMessage}
                setShowError={setShowError}
                showModel={showModel}
                resetModelPlacement={resetModelPlacement}
              />
              <div className="absolute top-4 left-4 z-50">
                <button
                  onClick={() => setUseNewARSystem(true)}
                  className="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm"
                >
                  Use New AR System
                </button>
              </div>
            </>
          )}
        </>
      )}

      {/* <Loader/> */}
    </CameraControlsErrorBoundary>
  )
}