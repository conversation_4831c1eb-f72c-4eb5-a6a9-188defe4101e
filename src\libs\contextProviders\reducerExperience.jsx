// reducerExperience.js
export const INITIAL_EXPERIENCE_STATE={
    firstPersonView:true,
    enableSnapView:false,
    showCart:false,
    showWishlist:false,
    mode360:true,
    modeModel:false,
    modeAR:false,
    textureIndex:false,
    controlsRef:null,
    activeRoomSnap: null,
    levelToHide: null,
}

export const ACTIONS_EXPERIENCE={
    MODE_360:'MODE_360',
    MODE_MODEL:'MODE_MODEL',
    MODE_AR:'MODE_AR',
    TOGGLE_CART:'TOGGLE_CART',
    TOGGLE_WISHLIST:'TOGGLE_WISHLIST',
    REF_CONTROL:'REF_CONTROL',
    TEXTURE_INDEX:'TEXTURE_INDEX',
    SET_FIRST_PERSON_VIEW: 'SET_FIRST_PERSON_VIEW',
    SET_ACTIVE_ROOM_SNAP: 'SET_ACTIVE_ROOM_SNAP',  
    DISABLE_ROOM_SNAP: 'DISABLE_ROOM_SNAP',  
    LEVEL_TO_HIDE: 'LEVEL_TO_HIDE',  
}

export const reducerExperience=(state,action)=>{
    switch (action.type) {
        case 'MODE_360':
            return{
                ...state,
                mode360:true,
                modeModel:false,
                modeAR:false,
                firstPersonView:true,
            }
        case 'MODE_MODEL':
            return{
                ...state,
                mode360:false,
                modeModel:true,
                modeAR:false,
                firstPersonView:false,
                enableSnapView:false,
            }
        case 'MODE_AR':
            return{
                ...state,
                mode360:false,
                modeModel:false,
                modeAR:true,
            }
        case 'TOGGLE_CART':
            return{
                ...state,
                showCart:!state?.showCart,
            }
        case 'TEXTURE_INDEX':
            return{
                ...state,
                textureIndex:action?.payload,
            }
        case 'TOGGLE_WISHLIST':
            return{
                ...state,
                showWishlist:!state?.showWishlist
            }
        case 'REF_CONTROL':
            return{
                ...state,
                controlsRef: action?.payload,
            }
        case 'SET_FIRST_PERSON_VIEW':
            return {
                ...state,
                firstPersonView: action?.payload,
            }
        case 'SET_ACTIVE_ROOM_SNAP':
            return {
                ...state,
                activeRoomSnap: action.payload,
                enableSnapView: true,
            }
        case 'DISABLE_ROOM_SNAP':
            return {
                ...state,
                enableSnapView: false,
            }
        case 'LEVEL_TO_HIDE':
            return {
                ...state,
                levelToHide: action.payload,
            }
        default:
            return state;
    }
}