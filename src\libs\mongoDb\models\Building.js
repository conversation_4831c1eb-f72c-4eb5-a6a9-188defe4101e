import mongoose from 'mongoose';
const { Schema } = mongoose;

// Building Summary sub-schema
const buildingSummarySchema = new Schema({
    length: { type: Number, required: true },
    width: { type: Number, required: true },
    baths: { type: Number, required: true },
    levels: { type: Number, required: true },
    cars: { type: Number, required: true },
    beds: { type: Number, required: true }
}, { _id: false });

// Building Highlights sub-schema
const buildingHighlightSchema = new Schema({
    title: { type: String, required: true },
    description: { type: String, required: true }
}, { _id: false });

// File object sub-schema
const fileSchema = new Schema({
    id: { type: String, required: true },
    name: { type: String, required: true },
    url: { type: String, required: true },
    firebaseUrl: { type: String },
    size: { type: Number },
    type: { type: String },
    priority: { type: Number } // For hideLevel files
}, { _id: false });

const buildingSchema = new Schema({
    // Text input fields
    projectTitle: { type: String, required: true, unique: true },
    price: { type: String, required: true },
    buildingTitle: { type: String, required: true },
    buildingType: {
        type: String,
        required: true,
        enum: ['multi-storey', 'single-storey', 'multi-residence']
    },
    desc: { type: String, required: true },
    position: { type: String, required: true },
    arPosition: { type: String, required: true },
    minDistance: { type: String, required: true },
    maxDistance: { type: String, required: true },
    buildingRole: { type: String, default: 'exhibit' },
    features: { type: String, required: true },
    outroSection: { type: String, required: true },
    warrantyInformation: { type: String },
    shippingInformation: { type: String },
    returnPolicy: { type: String },

    // Object field
    buildingSummary: { type: buildingSummarySchema, required: true },

    // Array fields with unique validation
    buildingHighlights: { type: [buildingHighlightSchema], required: true },
    tags: { type: [String], default: [] },
    color: { type: [String], default: [] },
    collections: { type: [String], default: [] },

    // File upload fields - Images
    renders: { type: [fileSchema], default: [] },
    drawings: { type: [fileSchema], default: [] },
    _360sImages: { type: [fileSchema], default: [] },

    // File upload fields - 3D Models (.glb)
    modelsFiles: { type: [fileSchema], default: [] },
    hideLevel: { type: [fileSchema], default: [] },
    supportFiles: { type: [fileSchema], default: [] },
    roomSnaps: { type: [fileSchema], default: [] },

    // File upload fields - PDFs
    presentationDrawings: { type: [fileSchema], default: [] },
    constructionDrawingsPdf: { type: [fileSchema], default: [] },

    // File upload fields - CAD Files (.dwg)
    constructionDrawingsDwg: { type: [fileSchema], default: [] },

    // Additional fields
    comments: { type: Array, default: [] },
    likes: { type: Array, default: [] },
    rating: { type: String },
    thumbnail: { type: String },
    reviews: { type: Object, default: {} },
}, { timestamps: true });

// Indexes for better performance
buildingSchema.index({ projectTitle: 1 });
buildingSchema.index({ buildingType: 1 });
buildingSchema.index({ createdAt: -1 });

export const Building = mongoose.models.Building || mongoose.model('Building', buildingSchema);