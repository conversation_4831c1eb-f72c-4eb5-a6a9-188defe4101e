'use client';

/**
 * Console Error Suppression Utility
 * Suppresses known non-critical console errors during development
 */

// List of error patterns to suppress (use with caution)
const SUPPRESSED_ERROR_PATTERNS = [
  // Font loading errors that don't affect functionality
  /Failed to load resource.*font/i,
  /net::ERR_FAILED.*font/i,

  // Google Fonts specific errors
  /fonts\.googleapis\.com.*net::ERR_FAILED/i,
  /fonts\.gstatic\.com.*net::ERR_FAILED/i,

  // Turbopack development warnings
  /Module not found.*turbopack/i,

  // Three.js development warnings that are non-critical
  /THREE\.WebGLRenderer.*context lost/i,

  // Next.js development warnings
  /Warning.*useLayoutEffect.*server/i,

  // NextAuth.js adapter errors (now fixed but keeping for reference)
  /\[auth\]\[error\] MissingAdapter/i,
  /Email login requires an adapter/i,
];

// List of warning patterns to suppress
const SUPPRESSED_WARNING_PATTERNS = [
  // React development warnings that are expected
  /Warning.*validateDOMNesting/i,
  /Warning.*useLayoutEffect.*server-side/i,
  
  // Three.js warnings that are expected in development
  /THREE\.WebGLRenderer.*precision/i,
];

/**
 * Initialize console error suppression
 * Only runs in development mode
 */
export function initializeConsoleErrorSuppression() {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  // Store original console methods
  const originalError = console.error;
  const originalWarn = console.warn;

  // Override console.error
  console.error = (...args) => {
    const message = args.join(' ');
    
    // Check if this error should be suppressed
    const shouldSuppress = SUPPRESSED_ERROR_PATTERNS.some(pattern => 
      pattern.test(message)
    );
    
    if (!shouldSuppress) {
      originalError.apply(console, args);
    } else {
      // Optionally log suppressed errors to a different method
      if (process.env.NEXT_PUBLIC_DEBUG_SUPPRESSED_ERRORS === 'true') {
        console.debug('Suppressed error:', ...args);
      }
    }
  };

  // Override console.warn
  console.warn = (...args) => {
    const message = args.join(' ');
    
    // Check if this warning should be suppressed
    const shouldSuppress = SUPPRESSED_WARNING_PATTERNS.some(pattern => 
      pattern.test(message)
    );
    
    if (!shouldSuppress) {
      originalWarn.apply(console, args);
    } else {
      // Optionally log suppressed warnings to a different method
      if (process.env.NEXT_PUBLIC_DEBUG_SUPPRESSED_ERRORS === 'true') {
        console.debug('Suppressed warning:', ...args);
      }
    }
  };

  // Log that suppression is active
  console.info('🔇 Console error suppression active (development mode)');
  console.info('Set NEXT_PUBLIC_DEBUG_SUPPRESSED_ERRORS=true to see suppressed messages');
}

/**
 * Restore original console methods
 */
export function restoreConsole() {
  // This would require storing the original methods globally
  // For now, just refresh the page to restore
  console.info('To restore original console, refresh the page');
}

// Auto-initialize if in browser environment
if (typeof window !== 'undefined') {
  initializeConsoleErrorSuppression();
}
